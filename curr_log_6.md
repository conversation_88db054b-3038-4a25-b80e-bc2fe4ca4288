# CarbonCoin 开发日志 v6

## 最新完成功能 (2025-09-07)

### 卡片显示组件重构

**功能描述**：
完成了全新的 ItemCardView 组件开发，实现了两种显示样式的卡片视图，支持完整版和缩略版显示模式。

**技术实现**：

1. **ItemCardView 主组件**：

   - 定义了 ItemCardDisplayStyle 枚举（full/compact）
   - 统一的卡片数据处理逻辑
   - 支持 UserItemCard 和 ItemCard 数据模型
   - 容错处理，避免数据缺失导致的崩溃

2. **FullItemCardView 完整版卡片**：

   - 主题色背景支持（基于卡片 themeColor 字段）
   - 图片区域：支持透明背景主体的描边效果（非风景照）
   - 内容区域：标题和描述的完整显示
   - 底部信息：位置信息和创建时间
   - 圆角和阴影效果，符合设计规范

3. **CompactItemCardView 缩略版卡片**：

   - 横向布局，适合列表显示
   - 左侧：缩略图和标题
   - 右侧：位置和时间信息
   - 同样支持主体描边效果
   - 紧凑的间距和字体设计

4. **辅助功能**：
   - 自定义圆角扩展（支持指定角的圆角）
   - 日期格式化（yy/M/d 格式）
   - 完整的预览支持

**关键特性**：

- ✅ 两种显示样式（完整版/缩略版）
- ✅ 主题色背景支持
- ✅ 透明主体描边效果（2px 黑色+4px 白色）
- ✅ 风景照无描边处理
- ✅ 地理位置信息显示
- ✅ 创建时间格式化
- ✅ 容错处理和默认值
- ✅ 符合项目设计规范

**文件修改**：

- `CarbonCoin/Views/Components/ItemCardView.swift` - 全新实现卡片显示组件

### 卡片创建流程优化

**功能描述**：
完成了卡片创建流程的重大优化，支持风景卡片和购物卡片两种类型，并为购物卡片添加了主题色提取功能。

**技术实现**：

1. **ImageProcess.swift 服务层增强**：

   - 新增 `extractThemeColor(from:)` 方法，使用 CIAreaAverage 滤镜计算图片平均色
   - 返回十六进制颜色字符串格式（如 "4B7905"）
   - 支持透明背景主体的主题色提取

2. **ImageProcessViewModel 状态管理**：

   - 添加 `selectedCardType` 和 `showCardTypeSelection` 状态
   - 添加 `extractedThemeColor` 存储提取的主题色
   - 实现 `selectCardType(_:)` 方法处理不同卡片类型的流程分支
   - 风景卡片：跳过主体提取，直接进入分析
   - 购物卡片：进入主体提取流程，完成后自动提取主题色

3. **ImageProcessView UI 增强**：

   - 添加卡片类型选择界面，包含风景卡片和购物卡片两个选项
   - 风景卡片：山峰图标，绿色主题，"直接使用原图"
   - 购物卡片：购物袋图标，黄色主题，"提取主体物品"
   - 支持重新选择卡片类型功能

4. **CardStore 保存逻辑升级**：
   - `saveCard` 方法新增 `cardType` 和 `themeColor` 参数
   - 根据卡片类型设置不同的奖励：购物卡片（15 币+8 经验）vs 风景卡片（10 币+5 经验）
   - 正确传递卡片类型和主题色到后端 API

**关键特性**：

- ✅ 卡片类型选择（风景/购物）
- ✅ 主题色自动提取（购物卡片）
- ✅ 差异化奖励机制
- ✅ 流程分支处理
- ✅ UI 交互优化

### 卡片库显示系统重构

**功能描述**：
完全重构了 ItemCardLibrary，实现了基于 UserItemCard 的卡片展示系统，支持搜索、筛选和详情查看。

**技术实现**：

1. **EmptyCardListView 空状态组件**：

   - 支持三种筛选状态的空视图（所有/我创建的/我接收的）
   - 不同状态显示对应的图标、标题和描述文案
   - 为"所有"状态提供创建引导提示

2. **ItemCardLibrary 核心重构**：

   - 使用 `UserItemCardViewModel` 管理用户卡片数据
   - 实现三种筛选模式：所有、我创建的、我接收的
   - 支持按卡片标题进行实时搜索
   - 使用 `LazyVStack` 显示 `ItemCardView` 缩略版
   - 点击卡片弹出 Sheet 显示完整版详情

3. **UI 布局优化**：
   - 顶部搜索框 + 右侧筛选 Picker 的横向布局
   - 卡片列表垂直滚动，支持大量数据
   - Sheet 详情页支持中等和大尺寸显示
   - 保持项目整体设计风格和主题色彩

**关键特性**：

- ✅ 基于 UserItemCard 的数据展示
- ✅ 三种筛选模式（所有/创建/接收）
- ✅ 实时搜索功能
- ✅ 缩略版列表 + 详情 Sheet
- ✅ 空状态处理
- ✅ 响应式布局设计

**文件修改**：

- `CarbonCoin/Services/Imgae/ImageProcess.swift` - 添加主题色提取方法
- `CarbonCoin/ViewModels/ImageProcessViewModel.swift` - 卡片类型选择状态管理
- `CarbonCoin/Views/Core/ImageProcessView.swift` - 卡片类型选择 UI
- `CarbonCoin/Models/CardStore.swift` - saveCard 方法参数扩展
- `CarbonCoin/Views/Core/ItemCardLibrary.swift` - 完全重构卡片库显示
- `CarbonCoin/Views/PlaceholderVIews/EmptyCardListView.swift` - 新建空状态组件
- `CarbonCoin/Views/Components/ItemCardView.swift` - 全新实现卡片显示组件

## 历史完成功能 (2025-09-06)

### 记录详情视图和编辑功能完善

**功能描述**：
完成了记录详情视图的开发，包括详细的日志展示、点赞评论交互、编辑功能和日志标签页集成。

**技术实现**：

1. **RecordPuclicItem 详情视图组件**：

   - 左侧动态图标显示（根据记录类型选择）
   - 右侧内容框：描述、活动组件、地点奖励、时间、图片网格
   - 点赞者头像列表（横向滚动，支持超过 10 个的省略显示）
   - 完整的评论区（支持回复功能，自动添加@前缀）
   - 设置菜单（公开/私有切换、编辑、删除功能）
   - 实时点赞/取消点赞交互

2. **RecordEditView 编辑页面**：

   - 描述文本编辑（多行输入支持）
   - 图片管理（添加、删除、最多 6 张）
   - 图片上传集成（使用 ImageShareService 和 ImageEditor）
   - 活动组件占位视图（根据记录类型显示不同内容）
   - 评论点赞统计预览
   - 完整的保存和取消逻辑

3. **RecordDetailView 导航容器**：

   - 包装详情视图用于导航
   - 自动获取当前用户 ID
   - 统一的导航栏样式

4. **RecordItemList 日志标签页集成**：

   - 将详情视图集成到日志标签页
   - 公开日志筛选和分组显示
   - 时间轴样式保持一致
   - 空状态视图（EmptyLogsView）
   - 加载更多和刷新功能

5. **RecordItem 简略视图优化**：
   - 移除详细视图相关代码
   - 简化初始化参数
   - 保持简洁的卡片样式

**关键特性**：

- ✅ 完整的记录详情展示（图标、内容、图片、统计）
- ✅ 实时点赞/取消点赞功能
- ✅ 评论系统（支持回复和@提及）
- ✅ 设置菜单（公开性、编辑、删除）
- ✅ 图片编辑和上传功能
- ✅ 活动类型相关组件占位
- ✅ 日志标签页完整集成
- ✅ 时间轴样式统一
- ✅ 空状态处理

**文件修改**：

- `CarbonCoin/Views/Components/RecordPuclicItem.swift` - 全新实现详情视图
- `CarbonCoin/Views/Components/RecordEditView.swift` - 全新实现编辑页面
- `CarbonCoin/Views/Components/RecordDetailView.swift` - 新建导航容器
- `CarbonCoin/Views/Components/RecordItemList.swift` - 集成详情视图到日志标签页
- `CarbonCoin/Views/Components/RecordItem.swift` - 简化为纯简略视图

## 历史完成功能 (2025-09-05)

### 道具交互功能完善

**功能描述**：
完善了道具交互功能的三个主要部分：自动弹窗显示未读消息、详细弹窗内容设计和道具发送交互功能。

**技术实现**：

1. **PopupPropSheet 详细弹窗组件**：

   - 设计了美观的道具消息详细弹窗界面
   - 包含发送时间、发送者头像和昵称显示
   - 集成 LottieHelperView 显示道具动画效果
   - 使用 propTitle 作为动画文件名（如"赶路蜂.json"）
   - 显示道具名称和备注信息
   - 支持动画效果和交互反馈

2. **MapView 道具按钮集成**：

   - 在底部工具栏添加道具按钮（替换表情按钮）
   - 添加道具相关状态变量：showPropOverlay、showPropTransferSheet、showUnreadPropSheet
   - 参考卡片按钮的实现方式和交互逻辑
   - 支持动画切换和状态管理

3. **PropTransferSheet 道具发送界面**：

   - 参考 CardTransferSheet 设计，实现完整的道具发送功能
   - 道具选择区域：横向滚动显示所有可用道具，支持选择状态
   - 备注输入区域：支持多行文本输入和默认备注
   - 好友选择区域：网格布局显示在线好友，支持点击发送
   - 集成 LottieHelperView 在道具选择中显示动画
   - 完整的错误处理和成功提示

4. **动画效果集成**：
   - 在 PopupPropSheet 中使用 LottieHelperView 显示道具动画
   - 在 PropTransferSheet 中为每个道具项添加动画效果
   - 动画文件名基于 propTitle（从 PopsInfo.json 获取）
   - 支持循环播放和缩放动画效果

**关键特性**：

- ✅ 详细的道具消息弹窗（包含动画效果）
- ✅ MapView 中的道具发送入口
- ✅ 完整的道具选择和发送界面
- ✅ Lottie 动画集成（基于道具标题）
- ✅ 好友选择和实时状态显示
- ✅ 自定义备注和默认备注支持
- ✅ 完整的错误处理和用户反馈

**文件修改**：

- `CarbonCoin/Views/Sheets/PopupPropSheet.swift` - 实现详细道具消息弹窗
- `CarbonCoin/Views/Sheets/PropTransferSheet.swift` - 重新实现为拖拽式道具发送界面（参考 ItemCardSheetView）
- `CarbonCoin/Views/Components/FriendOnMapView.swift` - 添加道具拖放支持
- `CarbonCoin/Views/Core/MapView.swift` - 添加道具按钮和相关状态（需手动完成）

**最新更新**：

- ✅ 修正了 PropTransferSheet 的设计思路，改为拖拽式交互（类似卡片拖拽）
- ✅ 实现了 DraggablePropThumbnail 组件，支持长按拖拽
- ✅ 修改了 FriendOnMapView 同时支持卡片和道具的拖放
- ✅ 编译测试通过（iPhone 16, iOS 18.6 模拟器）

### 道具交互消息全局提示系统

**功能描述**：
实现了基于 PopupView 库的道具交互消息全局提示效果，用户在任何页面都能收到新的道具交互消息通知。

**技术实现**：

1. **PropInteractionViewModel 增强**：

   - 添加 `showNewMessagePopup` 属性控制弹窗显示
   - 实现轮询机制，每 30 秒检查一次未读消息
   - 添加 `startPollingUnreadMessages()` 和 `stopPollingUnreadMessages()` 方法
   - 实现 `checkForNewMessages()` 检测新消息逻辑
   - 添加 `latestUnreadMessage` 计算属性获取最新未读消息

2. **PropInteractionPopupView 组件**：

   - 自定义弹窗视图，包含发送者头像和消息内容
   - 使用毛玻璃效果和品牌绿色边框
   - 支持头像异步加载和默认头像显示
   - 智能时间格式化（刚刚、分钟前、小时前等）

3. **MainTabView 集成**：
   - 导入 PopupView 库
   - 创建 PropInteractionViewModel 实例
   - 使用 `.popup()` 修饰符集成弹窗功能
   - 配置弹窗样式：顶部浮动、3 秒自动隐藏、支持点击关闭
   - 添加生命周期管理（onAppear/onDisappear）

**关键特性**：

- ✅ 全局消息提示（任何页面都能看到）
- ✅ 30 秒轮询检查新消息
- ✅ 智能检测未读消息数量变化
- ✅ 美观的弹窗设计（毛玻璃效果）
- ✅ 自动隐藏和手动关闭支持
- ✅ 资源清理和内存管理

**文件修改**：

- `CarbonCoin/ViewModels/PropInteractionViewModel.swift` - 添加轮询和弹窗控制逻辑
- `CarbonCoin/Views/Components/PropInteractionPopupView.swift` - 新建弹窗组件
- `CarbonCoin/Views/MainTabView.swift` - 集成弹窗功能
- `CarbonCoin/Models/PropInteraction.swift` - 修复 API 响应解析问题（receiver 字段可选）

**问题修复**：

- ✅ 修复了 API 响应中缺少 receiver 字段导致的解析错误
- ✅ 将 PropInteraction 模型中的 receiver 字段改为可选类型
- ✅ 确保弹窗组件能正确处理缺少 receiver 信息的情况

## 项目整体状态

### 已完成的核心功能

1. **用户认证系统** - 登录/注册/用户信息管理
2. **健康数据集成** - HealthKit 步数/卡路里追踪
3. **位置服务** - 自动位置更新和地理编码
4. **道具交互系统** - 完整的道具发送/接收/管理功能
5. **宠物系统** - 虚拟宠物展示和管理
6. **足迹记录** - 出行打卡和统计
7. **全局消息提示** - 道具交互消息实时通知 ✨ 新增

### 技术架构

- **MVVM 架构模式** - 清晰的视图/视图模型/模型分离
- **SwiftUI + Combine** - 现代化的 UI 框架和响应式编程
- **网络层封装** - 统一的 API 请求处理
- **依赖注入** - 服务层协议化设计
- **PopupView 集成** - 优雅的弹窗提示效果

### 下一步计划

1. **活动组件实现** - 完善记录详情和编辑页面中的活动相关组件（出行、地点、识别）
2. **图片管理优化** - 实现从 COS 删除图片的功能和图片压缩优化
3. **评论系统增强** - 添加评论删除、举报功能和表情支持
4. **MapView 手动集成** - 完成 MapView.swift 中道具按钮和相关逻辑的手动集成
5. **道具动画资源** - 添加对应的 Lottie 动画文件（赶路蜂.json、迷路猫.json）
6. **推送通知** - 集成 APNs 实现后台消息推送

### 技术债务

- **MapView 编译错误** - 由于编辑器问题，MapView.swift 中的道具相关状态变量和初始化需要手动修复
- **道具动画资源** - 需要添加对应的 Lottie 动画资源文件（赶路蜂.json、迷路猫.json）
- **拖拽交互优化** - 需要实现全局拖拽状态管理和视觉反馈
- 需要添加网络状态检测，避免无网络时的无效轮询
- 考虑使用 WebSocket 替代轮询机制提高实时性
- 添加单元测试覆盖新增的道具交互逻辑

### 当前编译错误

MapView.swift 存在以下编译错误需要手动修复：

1. `_propInteractionViewModel` 声明问题
2. 缺少 `showPropTransferSheet` 和 `showUnreadPropSheet` 状态变量
3. PropInteractionViewModel 初始化方式需要调整

## 最新修复进度 (2025-01-07)

### 数据模型重构和编译错误修复

**当前状态**：正在进行后端 API 适配和数据模型更新

**已完成的修复**：

1. **ItemCard 模型更新**

   - 移除了 tags 字段，改为使用 cardType 枚举
   - 添加了 themeColor、coinReward、experienceReward 字段
   - 更新了所有相关的初始化和使用代码

2. **UserItemCard 模型更新**

   - 移除了 remark 字段（个性化备注功能已移除）
   - 将 isOwner 字段重命名为 isAuthor
   - 更新了相关的 ViewModel 和 View 代码

3. **编译错误修复进展**
   - ✅ 修复了 ItemCardManager 中的 API 调用参数
   - ✅ 更新了 CardStore 中的卡片创建逻辑
   - ✅ 修复了 ScanView 中的标签显示逻辑
   - ✅ 更新了 UserItemCardViewModel 中的方法签名

**当前问题**：

- ❌ ItemCardView.swift 文件存在结构性问题，需要完全重构
- ❌ 视图组件的作用域和依赖关系需要重新整理

**修复思路**：
由于 ItemCardView.swift 文件过于复杂，建议：

1. 将复杂的视图拆分为更小的独立组件
2. 简化数据流和状态管理
3. 移除不必要的功能（如已废弃的备注编辑）
4. 重新组织文件结构

**下一步计划**：

1. 完成 ItemCardView.swift 的完整重构
2. 确保所有编译错误得到解决
3. 测试所有卡片相关功能
4. 验证后端 API 集成

## 开发规范遵循

- ✅ 使用中文注释
- ✅ 遵循 MVVM 架构
- ✅ 统一的错误处理
- ✅ 内存管理和资源清理
- ✅ 响应式状态管理
